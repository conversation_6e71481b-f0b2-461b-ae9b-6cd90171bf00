#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HTML Processor for Lord of Mysteries Arabic Text Diacritization
==============================================================

This script is specifically designed to process the HTML files in the 
"Lord of Mysteries Volume 2: Faceless [Arabic]" project and add diacritics
to all Arabic text content.

Author: AI Assistant
Date: 2025-08-22
"""

import os
import sys
import re
import time
from pathlib import Path
from typing import List, Dict, Tuple
import logging
from bs4 import BeautifulSoup, NavigableString
import html

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('html_processing.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class LordOfMysteriesHTMLProcessor:
    """
    Specialized HTML processor for Lord of Mysteries Arabic text files.
    """
    
    def __init__(self):
        """Initialize the processor with Mishkal library."""
        try:
            import mishkal.tashkeel
            self.vocalizer = mishkal.tashkeel.TashkeelClass()
            logger.info("Mishkal library initialized successfully")
        except ImportError as e:
            logger.error(f"Failed to import Mishkal library: {e}")
            logger.error("Please install required packages: pip install -r requirements.txt")
            sys.exit(1)
        
        # Statistics
        self.stats = {
            'files_processed': 0,
            'files_failed': 0,
            'text_elements_processed': 0,
            'characters_diacritized': 0
        }
    
    def is_arabic_text(self, text: str) -> bool:
        """Check if text contains Arabic characters."""
        if not text or not text.strip():
            return False
        
        # Arabic Unicode ranges
        arabic_pattern = re.compile(r'[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]')
        return bool(arabic_pattern.search(text))
    
    def clean_arabic_text(self, text: str) -> str:
        """Clean Arabic text by removing existing diacritics and normalizing."""
        if not text:
            return text
        
        # Remove existing diacritics (tashkeel marks)
        diacritics_pattern = re.compile(r'[\u064B-\u0652\u0670\u0640]')
        cleaned = diacritics_pattern.sub('', text)
        
        # Normalize whitespace
        cleaned = re.sub(r'\s+', ' ', cleaned).strip()
        
        return cleaned
    
    def diacritize_arabic_text(self, text: str) -> str:
        """Apply diacritics to Arabic text using Mishkal."""
        if not text or not text.strip():
            return text
        
        if not self.is_arabic_text(text):
            return text
        
        try:
            # Clean the text first
            cleaned_text = self.clean_arabic_text(text)
            
            if not cleaned_text:
                return text
            
            # Apply diacritization
            diacritized = self.vocalizer.tashkeel(cleaned_text)
            
            # Post-process: clean up extra spaces
            diacritized = re.sub(r'\s+', ' ', diacritized).strip()
            
            # Update statistics
            if diacritized != cleaned_text:
                self.stats['characters_diacritized'] += len(diacritized) - len(cleaned_text)
            
            return diacritized
            
        except Exception as e:
            logger.warning(f"Failed to diacritize text '{text[:30]}...': {e}")
            return text
    
    def process_text_nodes(self, soup: BeautifulSoup) -> int:
        """
        Process all text nodes in the HTML document.
        
        Args:
            soup: BeautifulSoup object
            
        Returns:
            int: Number of text elements processed
        """
        processed_count = 0
        
        # Find all elements with lang="ar" attribute
        arabic_elements = soup.find_all(attrs={"lang": "ar"})
        
        for element in arabic_elements:
            # Process direct text content
            for content in element.contents:
                if isinstance(content, NavigableString):
                    original_text = str(content).strip()
                    
                    if original_text and self.is_arabic_text(original_text):
                        diacritized_text = self.diacritize_arabic_text(original_text)
                        
                        if diacritized_text != original_text:
                            content.replace_with(diacritized_text)
                            processed_count += 1
                            logger.debug(f"Processed: {original_text[:30]}... -> {diacritized_text[:30]}...")
        
        # Also check for any Arabic text in elements without lang="ar" attribute
        # This is a fallback in case some Arabic text doesn't have the lang attribute
        all_text_nodes = soup.find_all(text=True)
        
        for text_node in all_text_nodes:
            # Skip if already processed (has lang="ar" parent)
            if text_node.parent and text_node.parent.get('lang') == 'ar':
                continue
            
            original_text = str(text_node).strip()
            
            if original_text and self.is_arabic_text(original_text):
                diacritized_text = self.diacritize_arabic_text(original_text)
                
                if diacritized_text != original_text:
                    text_node.replace_with(diacritized_text)
                    processed_count += 1
                    logger.debug(f"Fallback processed: {original_text[:30]}...")
        
        return processed_count
    
    def process_single_file(self, file_path: Path) -> bool:
        """
        Process a single HTML file.
        
        Args:
            file_path: Path to the HTML file
            
        Returns:
            bool: True if successful
        """
        try:
            logger.info(f"Processing: {file_path.name}")
            
            # Read the original file
            with open(file_path, 'r', encoding='utf-8') as f:
                original_content = f.read()
            
            # Parse with BeautifulSoup
            soup = BeautifulSoup(original_content, 'html.parser')
            
            # Process text nodes
            processed_count = self.process_text_nodes(soup)
            
            # Write back to file
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(str(soup))
            
            self.stats['files_processed'] += 1
            self.stats['text_elements_processed'] += processed_count
            
            logger.info(f"✓ {file_path.name}: {processed_count} text elements diacritized")
            return True
            
        except Exception as e:
            logger.error(f"✗ Failed to process {file_path.name}: {e}")
            self.stats['files_failed'] += 1
            return False
    
    def get_html_files(self, directory: Path) -> List[Path]:
        """Get all HTML files in the directory, sorted by name."""
        html_files = []
        
        # Look for index_split_*.html files specifically
        for file_path in directory.glob("index_split_*.html"):
            html_files.append(file_path)
        
        # Sort by the number in the filename
        def extract_number(file_path):
            match = re.search(r'index_split_(\d+)\.html', file_path.name)
            return int(match.group(1)) if match else 0
        
        html_files.sort(key=extract_number)
        
        return html_files
    
    def process_all_files(self, directory: Path = None) -> Dict[str, int]:
        """
        Process all HTML files in the project directory.
        
        Args:
            directory: Project directory (defaults to current directory)
            
        Returns:
            dict: Processing statistics
        """
        if directory is None:
            directory = Path.cwd()
        
        logger.info(f"Starting batch processing in: {directory}")
        
        # Get all HTML files
        html_files = self.get_html_files(directory)
        
        if not html_files:
            logger.warning("No HTML files found matching pattern 'index_split_*.html'")
            return self.stats
        
        logger.info(f"Found {len(html_files)} HTML files to process")
        
        # Process each file
        start_time = time.time()
        
        for i, file_path in enumerate(html_files, 1):
            logger.info(f"[{i}/{len(html_files)}] Processing {file_path.name}")
            
            success = self.process_single_file(file_path)
            
            if not success:
                logger.error(f"Failed to process {file_path.name}")
            
            # Show progress every 10 files
            if i % 10 == 0:
                elapsed = time.time() - start_time
                avg_time = elapsed / i
                remaining = (len(html_files) - i) * avg_time
                logger.info(f"Progress: {i}/{len(html_files)} files, "
                          f"ETA: {remaining/60:.1f} minutes")
        
        # Final statistics
        elapsed = time.time() - start_time
        logger.info(f"\n{'='*50}")
        logger.info(f"PROCESSING COMPLETE")
        logger.info(f"{'='*50}")
        logger.info(f"Total files processed: {self.stats['files_processed']}")
        logger.info(f"Files failed: {self.stats['files_failed']}")
        logger.info(f"Text elements diacritized: {self.stats['text_elements_processed']}")
        logger.info(f"Characters added: {self.stats['characters_diacritized']}")
        logger.info(f"Total time: {elapsed/60:.1f} minutes")
        logger.info(f"Average time per file: {elapsed/len(html_files):.2f} seconds")
        
        return self.stats


def main():
    """Main function."""
    import argparse
    
    parser = argparse.ArgumentParser(
        description="Process Lord of Mysteries HTML files to add Arabic diacritics"
    )
    
    parser.add_argument(
        '-d', '--directory',
        type=Path,
        default=Path.cwd(),
        help='Directory containing HTML files (default: current directory)'
    )
    
    parser.add_argument(
        '-f', '--file',
        type=Path,
        help='Process a single file instead of all files'
    )
    
    parser.add_argument(
        '-v', '--verbose',
        action='store_true',
        help='Enable verbose logging'
    )
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Initialize processor
    processor = LordOfMysteriesHTMLProcessor()
    
    if args.file:
        # Process single file
        if not args.file.exists():
            logger.error(f"File not found: {args.file}")
            sys.exit(1)
        
        success = processor.process_single_file(args.file)
        sys.exit(0 if success else 1)
    
    else:
        # Process all files
        if not args.directory.exists():
            logger.error(f"Directory not found: {args.directory}")
            sys.exit(1)
        
        stats = processor.process_all_files(args.directory)
        
        # Exit with error code if any files failed
        sys.exit(0 if stats['files_failed'] == 0 else 1)


if __name__ == "__main__":
    main()
