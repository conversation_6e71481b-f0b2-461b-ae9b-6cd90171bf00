#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Arabic Text Diacritization Tool for HTML Files
==============================================

This script processes HTML files containing Arabic text and adds diacritics (tashkeel)
to the Arabic content using the Mishkal library.

Author: AI Assistant
Date: 2025-08-22
"""

import os
import sys
import re
import argparse
from pathlib import Path
from typing import List, Optional
import logging
from bs4 import BeautifulSoup
import html

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('diacritization.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class ArabicDiacritizer:
    """
    A class to handle Arabic text diacritization in HTML files.
    """
    
    def __init__(self):
        """Initialize the diacritizer with Mishkal library."""
        try:
            import mishkal.tashkeel
            self.vocalizer = mishkal.tashkeel.TashkeelClass()
            logger.info("Mishkal library loaded successfully")
        except ImportError as e:
            logger.error(f"Failed to import Mishkal library: {e}")
            logger.error("Please install Mishkal: pip install mishkal")
            sys.exit(1)
    
    def is_arabic_text(self, text: str) -> bool:
        """
        Check if the text contains Arabic characters.
        
        Args:
            text (str): Text to check
            
        Returns:
            bool: True if text contains Arabic characters
        """
        arabic_pattern = re.compile(r'[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]')
        return bool(arabic_pattern.search(text))
    
    def clean_text_for_diacritization(self, text: str) -> str:
        """
        Clean text before diacritization by removing existing diacritics.
        
        Args:
            text (str): Input text
            
        Returns:
            str: Cleaned text
        """
        # Remove existing diacritics (tashkeel marks)
        diacritics_pattern = re.compile(r'[\u064B-\u0652\u0670\u0640]')
        cleaned = diacritics_pattern.sub('', text)
        return cleaned.strip()
    
    def diacritize_text(self, text: str) -> str:
        """
        Add diacritics to Arabic text using Mishkal.
        
        Args:
            text (str): Input Arabic text
            
        Returns:
            str: Diacritized text
        """
        if not text or not text.strip():
            return text
            
        if not self.is_arabic_text(text):
            return text
        
        try:
            # Clean the text first
            cleaned_text = self.clean_text_for_diacritization(text)
            
            # Apply diacritization
            diacritized = self.vocalizer.tashkeel(cleaned_text)
            
            # Clean up any extra spaces
            diacritized = re.sub(r'\s+', ' ', diacritized).strip()
            
            logger.debug(f"Original: {text[:50]}...")
            logger.debug(f"Diacritized: {diacritized[:50]}...")
            
            return diacritized
            
        except Exception as e:
            logger.warning(f"Failed to diacritize text: {e}")
            return text
    
    def process_html_file(self, input_file: Path, output_file: Optional[Path] = None) -> bool:
        """
        Process an HTML file and add diacritics to Arabic text.
        
        Args:
            input_file (Path): Input HTML file path
            output_file (Path, optional): Output file path. If None, overwrites input file.
            
        Returns:
            bool: True if processing was successful
        """
        try:
            # Read the HTML file
            with open(input_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Parse HTML with BeautifulSoup
            soup = BeautifulSoup(content, 'html.parser')
            
            # Find all text nodes that might contain Arabic text
            # Focus on elements with lang="ar" or containing Arabic text
            arabic_elements = soup.find_all(lambda tag: (
                tag.name and 
                (tag.get('lang') == 'ar' or 
                 (tag.string and self.is_arabic_text(tag.string)))
            ))
            
            processed_count = 0
            
            # Process each element containing Arabic text
            for element in arabic_elements:
                if element.string:
                    original_text = element.string
                    diacritized_text = self.diacritize_text(original_text)
                    
                    if diacritized_text != original_text:
                        element.string.replace_with(diacritized_text)
                        processed_count += 1
                
                # Also process direct text content in elements
                for text_node in element.find_all(text=True, recursive=False):
                    if text_node.strip() and self.is_arabic_text(text_node):
                        original_text = str(text_node)
                        diacritized_text = self.diacritize_text(original_text)
                        
                        if diacritized_text != original_text:
                            text_node.replace_with(diacritized_text)
                            processed_count += 1
            
            # If no output file specified, overwrite the input file
            if output_file is None:
                output_file = input_file
            
            # Write the processed HTML back to file
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(str(soup))
            
            logger.info(f"Processed {input_file}: {processed_count} text elements diacritized")
            return True
            
        except Exception as e:
            logger.error(f"Failed to process {input_file}: {e}")
            return False
    
    def process_directory(self, directory: Path, pattern: str = "*.html") -> int:
        """
        Process all HTML files in a directory.
        
        Args:
            directory (Path): Directory containing HTML files
            pattern (str): File pattern to match (default: "*.html")
            
        Returns:
            int: Number of successfully processed files
        """
        html_files = list(directory.glob(pattern))
        
        if not html_files:
            logger.warning(f"No HTML files found in {directory}")
            return 0
        
        logger.info(f"Found {len(html_files)} HTML files to process")
        
        success_count = 0
        
        for html_file in html_files:
            logger.info(f"Processing: {html_file.name}")
            
            if self.process_html_file(html_file):
                success_count += 1
            else:
                logger.error(f"Failed to process: {html_file.name}")
        
        logger.info(f"Successfully processed {success_count}/{len(html_files)} files")
        return success_count


def main():
    """Main function to handle command line arguments and execute diacritization."""
    parser = argparse.ArgumentParser(
        description="Add Arabic diacritics to HTML files using Mishkal library"
    )
    
    parser.add_argument(
        'input',
        help='Input HTML file or directory containing HTML files'
    )
    
    parser.add_argument(
        '-o', '--output',
        help='Output file (for single file) or directory (for directory processing)'
    )
    
    parser.add_argument(
        '-p', '--pattern',
        default='*.html',
        help='File pattern to match when processing directory (default: *.html)'
    )
    
    parser.add_argument(
        '-v', '--verbose',
        action='store_true',
        help='Enable verbose logging'
    )
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    input_path = Path(args.input)
    
    if not input_path.exists():
        logger.error(f"Input path does not exist: {input_path}")
        sys.exit(1)
    
    # Initialize the diacritizer
    diacritizer = ArabicDiacritizer()
    
    if input_path.is_file():
        # Process single file
        output_path = Path(args.output) if args.output else None
        success = diacritizer.process_html_file(input_path, output_path)
        sys.exit(0 if success else 1)
    
    elif input_path.is_dir():
        # Process directory
        success_count = diacritizer.process_directory(input_path, args.pattern)
        sys.exit(0 if success_count > 0 else 1)
    
    else:
        logger.error(f"Invalid input path: {input_path}")
        sys.exit(1)


if __name__ == "__main__":
    main()
