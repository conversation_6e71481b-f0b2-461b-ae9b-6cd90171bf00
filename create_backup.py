#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Create backup of test file
"""

import shutil
import os
from pathlib import Path

# Set the correct working directory
project_dir = Path(r"c:\Users\<USER>\Downloads\Tepub\Lord of Mysteries Volume 2_ Faceless [Arabic]")
os.chdir(project_dir)

print(f"Working directory: {os.getcwd()}")

# Create backup of test file
source = Path("index_split_006.html")
backup = Path("index_split_006_backup.html")

if source.exists():
    shutil.copy(source, backup)
    print(f"✓ Backup created: {backup}")
else:
    print(f"✗ Source file not found: {source}")
    # List available HTML files
    html_files = list(Path(".").glob("index_split_*.html"))
    print(f"Available HTML files: {len(html_files)}")
    if html_files:
        print(f"First few files: {[f.name for f in html_files[:5]]}")
